import asyncio
import aiohttp
import base64
import time
from typing import Dict, Optional
import json
from datetime import datetime

# 全局变量
current_cookie = None
cookie_update_time = 0
COOKIE_REFRESH_INTERVAL = 3600  # 60分钟刷新一次cookie


async def refresh_cookie() -> bool:
    """刷新cookie"""
    global current_cookie, cookie_update_time
    
    try:
        url = 'http://www.gxdlys.com/Wechat/Home/PostLogin'
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cache-Control': 'no-cache,no-store',
            'Pragma': 'no-cache'
        }
        data = {
            'loginName': 'Ewg8bByIRrFfxWoDjjT/bstpNt+JxRWj4Yx8Uk2k7ZU=',
            'password': 'HPcTRuc30cG6u7YNmWO+ug==',
            'wechatUid': ''
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, data=data) as response:
                cookies = response.cookies
                for cookie in cookies:
                    if cookie.key == 'CatstiProject.Core.Web':
                        current_cookie = cookie.value
                        cookie_update_time = time.time()
                        return True
        return False
    except Exception as e:
        print(f"刷新cookie失败: {str(e)}")
        return False

async def ensure_valid_cookie() -> str:
    """确保有效的cookie可用"""
    global current_cookie, cookie_update_time
    
    current_time = time.time()
    if not current_cookie or (current_time - cookie_update_time) > COOKIE_REFRESH_INTERVAL:
        if not await refresh_cookie():
            return None
    return current_cookie

async def fetch_id_card_info(id_card: str, name: str) -> Dict:
    """获取身份证信息和照片"""
    cookie = await ensure_valid_cookie()
    if not cookie:
        return None
    
    headers = {
        "Host": "www.gxdlys.com",
        "Connection": "keep-alive",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/tpg,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Cookie": f"CatstiProject.Core.Web={cookie}"
    }
    
    url = f"http://www.gxdlys.com/Wechat/FaceDetect/GetGAIDCardPhotoNew?idCard={id_card}&name={name}"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                content_type = response.headers.get('Content-Type', '')
                if 'application/json' not in content_type.lower():
                    # 如果返回不是JSON，可能是cookie失效
                    return None
                
                data = await response.json()
                
                if 'data' not in data:
                    return None
                    
                item1 = data['data'].get('item1')
                item2 = data['data'].get('item2')
                
                if not item2:
                    return None
                
                result = {
                    "姓名": name,
                    "身份证号": id_card,
                    "性别": item2.get("gender", ""),
                    "民族": item2.get("nation", ""),
                    "出生日期": item2.get("dob", ""),
                    "住址": item2.get("fulladdr", ""),
                    "签发机关": item2.get("issueD_UNIT", ""),
                    "有效期开始": item2.get("uL_FROM_DATE", ""),
                    "有效期结束": item2.get("uL_END_DATE", ""),
                    "更新日期": item2.get("d_GXSJ", ""),
                    "查询时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # 如果有照片ID，获取照片
                if item1:
                    img_url = f"http://www.gxdlys.com/System/FileService/ShowFile?fileId={item1}"
                    try:
                        async with session.get(img_url, headers=headers) as img_response:
                            img_data = await img_response.read()
                            result["photo"] = base64.b64encode(img_data).decode('utf-8')
                    except Exception as e:
                        print(f"获取照片失败 {id_card}: {str(e)}")
                        result["photo"] = None
                else:
                    result["photo"] = None
                
                return result
                    
    except (json.JSONDecodeError, aiohttp.ClientError) as e:
        print(f"获取数据失败 {id_card}: {str(e)}")
        return None

async def query_single_person(name: str, id_card: str) -> Dict:
    """查询单个人的身份证信息"""
    print(f"正在查询: {name} {id_card}")
    
    result = await fetch_id_card_info(id_card, name)
    
    if result is None:
        print(f"查询失败: {name} {id_card}")
        return None
    else:
        print(f"查询成功: {name} {id_card}")
        return result

def print_result(result: Dict):
    """打印查询结果"""
    if result is None:
        print("查询失败")
        return
    
    print("\n=== 查询结果 ===")
    print(f"姓名: {result.get('姓名', '')}")
    print(f"身份证号: {result.get('身份证号', '')}")
    print(f"性别: {result.get('性别', '')}")
    print(f"民族: {result.get('民族', '')}")
    print(f"出生日期: {result.get('出生日期', '')}")
    print(f"住址: {result.get('住址', '')}")
    print(f"签发机关: {result.get('签发机关', '')}")
    print(f"有效期开始: {result.get('有效期开始', '')}")
    print(f"有效期结束: {result.get('有效期结束', '')}")
    print(f"更新日期: {result.get('更新日期', '')}")
    print(f"查询时间: {result.get('查询时间', '')}")
    
    if result.get('photo'):
        print("照片: 已获取 (Base64编码)")
    else:
        print("照片: 未获取")
    print("================\n")

async def main():
    """主函数"""
    print("身份证信息查询工具")
    print("输入格式: 姓名 身份证号")
    print("输入 'quit' 或 'exit' 退出程序\n")
    
    while True:
        try:
            user_input = input("请输入姓名和身份证号 (用空格分隔): ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("程序退出")
                break
            
            if not user_input:
                continue
            
            try:
                parts = user_input.split()
                if len(parts) != 2:
                    print("输入格式错误，请输入: 姓名 身份证号")
                    continue
                
                name, id_card = parts
                result = await query_single_person(name, id_card)
                print_result(result)
                
            except Exception as e:
                print(f"查询过程中出错: {str(e)}")
                
        except KeyboardInterrupt:
            print("\n程序被中断")
            break
        except Exception as e:
            print(f"输入处理错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
