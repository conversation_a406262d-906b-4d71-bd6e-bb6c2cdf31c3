import asyncio
import aiohttp
import base64
import time
from typing import Dict, Optional
import json
from datetime import datetime
import logging
import ssl

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 全局变量
current_cookie = None
cookie_update_time = 0
COOKIE_REFRESH_INTERVAL = 3600  # 60分钟刷新一次cookie


async def refresh_cookie() -> bool:
    """刷新cookie"""
    global current_cookie, cookie_update_time

    logger.info("开始刷新cookie...")

    try:
        url = 'http://www.gxdlys.com/Wechat/Home/PostLogin'
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cache-Control': 'no-cache,no-store',
            'Pragma': 'no-cache'
        }
        data = {
            'loginName': 'Ewg8bByIRrFfxWoDjjT/bstpNt+JxRWj4Yx8Uk2k7ZU=',
            'password': 'HPcTRuc30cG6u7YNmWO+ug==',
            'wechatUid': ''
        }

        logger.info(f"发送登录请求到: {url}")
        logger.debug(f"请求头: {headers}")
        logger.debug(f"请求数据: {data}")

        # 创建SSL上下文，禁用证书验证
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        logger.info("已禁用SSL证书验证 (因为目标网站证书可能已过期)")

        # 创建连接器，使用自定义SSL上下文
        connector = aiohttp.TCPConnector(
            ssl=ssl_context,
            limit=100,
            limit_per_host=30,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )

        async with aiohttp.ClientSession(connector=connector) as session:
            start_time = time.time()
            async with session.post(url, headers=headers, data=data) as response:
                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # 转换为毫秒

                logger.info(f"登录请求响应: 状态码={response.status}, 响应时间={response_time:.2f}ms")
                logger.debug(f"响应头: {dict(response.headers)}")

                cookies = response.cookies
                logger.debug(f"收到cookies: {[f'{c.key}={c.value}' for c in cookies]}")

                for cookie in cookies:
                    if cookie.key == 'CatstiProject.Core.Web':
                        current_cookie = cookie.value
                        cookie_update_time = time.time()
                        logger.info(f"Cookie刷新成功: {cookie.value[:20]}...")
                        return True

                logger.warning("未找到目标cookie: CatstiProject.Core.Web")
        return False
    except ssl.SSLError as e:
        logger.error(f"SSL连接错误: {str(e)}")
        logger.info("建议检查网络连接或联系网站管理员")
        return False
    except aiohttp.ClientConnectorError as e:
        logger.error(f"连接错误: {str(e)}")
        logger.info("无法连接到服务器，请检查网络连接")
        return False
    except aiohttp.ClientTimeout as e:
        logger.error(f"请求超时: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"刷新cookie失败: {str(e)}")
        logger.debug("详细错误信息:", exc_info=True)
        return False

async def ensure_valid_cookie() -> str:
    """确保有效的cookie可用"""
    global current_cookie, cookie_update_time

    current_time = time.time()
    cookie_age = current_time - cookie_update_time if cookie_update_time > 0 else float('inf')

    logger.debug(f"检查cookie状态: 当前cookie={'存在' if current_cookie else '不存在'}, 年龄={cookie_age:.1f}秒")

    if not current_cookie or cookie_age > COOKIE_REFRESH_INTERVAL:
        logger.info(f"Cookie需要刷新 (年龄: {cookie_age:.1f}秒, 限制: {COOKIE_REFRESH_INTERVAL}秒)")
        if not await refresh_cookie():
            logger.error("Cookie刷新失败")
            return None
    else:
        logger.debug("Cookie仍然有效，无需刷新")

    return current_cookie

async def fetch_id_card_info(id_card: str, name: str) -> Dict:
    """获取身份证信息和照片"""
    logger.info(f"开始获取身份证信息: 姓名={name}, 身份证号={id_card}")

    cookie = await ensure_valid_cookie()
    if not cookie:
        logger.error("无法获取有效cookie")
        return None

    headers = {
        "Host": "www.gxdlys.com",
        "Connection": "keep-alive",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/tpg,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Cookie": f"CatstiProject.Core.Web={cookie}"
    }

    url = f"http://www.gxdlys.com/Wechat/FaceDetect/GetGAIDCardPhotoNew?idCard={id_card}&name={name}"
    logger.info(f"发送身份证查询请求: {url}")
    logger.debug(f"请求头: {headers}")

    try:
        # 创建SSL上下文，禁用证书验证
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # 创建连接器，使用自定义SSL上下文
        connector = aiohttp.TCPConnector(
            ssl=ssl_context,
            limit=100,
            limit_per_host=30,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )

        async with aiohttp.ClientSession(connector=connector) as session:
            start_time = time.time()
            async with session.get(url, headers=headers) as response:
                end_time = time.time()
                response_time = (end_time - start_time) * 1000

                logger.info(f"身份证查询响应: 状态码={response.status}, 响应时间={response_time:.2f}ms")
                logger.debug(f"响应头: {dict(response.headers)}")

                content_type = response.headers.get('Content-Type', '')
                logger.debug(f"响应内容类型: {content_type}")

                if 'application/json' not in content_type.lower():
                    logger.warning("响应不是JSON格式，可能是cookie失效")
                    response_text = await response.text()
                    logger.debug(f"响应内容: {response_text[:500]}...")
                    return None

                data = await response.json()
                logger.debug(f"响应JSON数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

                if 'data' not in data:
                    logger.warning("响应中没有data字段")
                    return None

                item1 = data['data'].get('item1')
                item2 = data['data'].get('item2')

                logger.debug(f"解析数据: item1={item1}, item2存在={'是' if item2 else '否'}")

                if not item2:
                    logger.warning("响应中没有item2数据")
                    return None

                result = {
                    "姓名": name,
                    "身份证号": id_card,
                    "性别": item2.get("gender", ""),
                    "民族": item2.get("nation", ""),
                    "出生日期": item2.get("dob", ""),
                    "住址": item2.get("fulladdr", ""),
                    "签发机关": item2.get("issueD_UNIT", ""),
                    "有效期开始": item2.get("uL_FROM_DATE", ""),
                    "有效期结束": item2.get("uL_END_DATE", ""),
                    "更新日期": item2.get("d_GXSJ", ""),
                    "查询时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }

                logger.info(f"成功解析身份证基本信息: {name}")

                # 如果有照片ID，获取照片
                if item1:
                    logger.info(f"开始获取照片: fileId={item1}")
                    img_url = f"http://www.gxdlys.com/System/FileService/ShowFile?fileId={item1}"
                    logger.debug(f"照片请求URL: {img_url}")

                    try:
                        img_start_time = time.time()
                        async with session.get(img_url, headers=headers) as img_response:
                            img_end_time = time.time()
                            img_response_time = (img_end_time - img_start_time) * 1000

                            logger.info(f"照片请求响应: 状态码={img_response.status}, 响应时间={img_response_time:.2f}ms")

                            if img_response.status == 200:
                                img_data = await img_response.read()
                                img_size = len(img_data)
                                result["photo"] = base64.b64encode(img_data).decode('utf-8')
                                logger.info(f"照片获取成功: 大小={img_size}字节, Base64长度={len(result['photo'])}字符")
                            else:
                                logger.warning(f"照片请求失败: 状态码={img_response.status}")
                                result["photo"] = None
                    except Exception as e:
                        logger.error(f"获取照片失败 {id_card}: {str(e)}")
                        result["photo"] = None
                else:
                    logger.info("没有照片ID，跳过照片获取")
                    result["photo"] = None

                logger.info(f"身份证信息获取完成: {name} {id_card}")
                return result

    except ssl.SSLError as e:
        logger.error(f"SSL连接错误 {id_card}: {str(e)}")
        return None
    except aiohttp.ClientConnectorError as e:
        logger.error(f"连接错误 {id_card}: {str(e)}")
        return None
    except aiohttp.ClientTimeout as e:
        logger.error(f"请求超时 {id_card}: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析失败 {id_card}: {str(e)}")
        return None
    except aiohttp.ClientError as e:
        logger.error(f"HTTP客户端错误 {id_card}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取数据失败 {id_card}: {str(e)}")
        logger.debug("详细错误信息:", exc_info=True)
        return None

async def query_single_person(name: str, id_card: str, max_retries: int = 3) -> Dict:
    """查询单个人的身份证信息，支持重试"""
    logger.info(f"=" * 60)
    logger.info(f"开始查询身份证信息")
    logger.info(f"姓名: {name}")
    logger.info(f"身份证号: {id_card}")
    logger.info(f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"最大重试次数: {max_retries}")
    logger.info(f"=" * 60)

    for attempt in range(max_retries + 1):
        if attempt > 0:
            logger.info(f"第 {attempt} 次重试...")
            await asyncio.sleep(2)  # 重试前等待2秒

        result = await fetch_id_card_info(id_card, name)

        if result is not None:
            logger.info(f"查询成功: {name} {id_card} (尝试次数: {attempt + 1})")
            logger.info(f"=" * 60)
            return result

        if attempt < max_retries:
            logger.warning(f"第 {attempt + 1} 次尝试失败，准备重试...")

    logger.error(f"查询失败: {name} {id_card} (已尝试 {max_retries + 1} 次)")
    logger.info(f"=" * 60)
    return None

def print_result(result: Dict):
    """打印查询结果"""
    if result is None:
        print("查询失败")
        return
    
    print("\n=== 查询结果 ===")
    print(f"姓名: {result.get('姓名', '')}")
    print(f"身份证号: {result.get('身份证号', '')}")
    print(f"性别: {result.get('性别', '')}")
    print(f"民族: {result.get('民族', '')}")
    print(f"出生日期: {result.get('出生日期', '')}")
    print(f"住址: {result.get('住址', '')}")
    print(f"签发机关: {result.get('签发机关', '')}")
    print(f"有效期开始: {result.get('有效期开始', '')}")
    print(f"有效期结束: {result.get('有效期结束', '')}")
    print(f"更新日期: {result.get('更新日期', '')}")
    print(f"查询时间: {result.get('查询时间', '')}")
    
    if result.get('photo'):
        print("照片: 已获取 (Base64编码)")
    else:
        print("照片: 未获取")
    print("================\n")

async def test_connection():
    """测试网络连接"""
    logger.info("测试网络连接...")

    try:
        # 创建SSL上下文，禁用证书验证
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        connector = aiohttp.TCPConnector(ssl=ssl_context)

        async with aiohttp.ClientSession(connector=connector) as session:
            test_url = "http://www.gxdlys.com"
            start_time = time.time()
            async with session.get(test_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                end_time = time.time()
                response_time = (end_time - start_time) * 1000

                logger.info(f"连接测试成功: 状态码={response.status}, 响应时间={response_time:.2f}ms")
                return True

    except Exception as e:
        logger.error(f"连接测试失败: {str(e)}")
        return False

async def main():
    """主函数"""
    logger.info("身份证信息查询工具启动")
    logger.info("日志级别: INFO")
    logger.info("Cookie刷新间隔: {}秒".format(COOKIE_REFRESH_INTERVAL))

    # 测试网络连接
    if not await test_connection():
        print("网络连接测试失败，程序可能无法正常工作")
        print("请检查网络连接后重试")
        return

    print("身份证信息查询工具")
    print("输入格式: 姓名 身份证号")
    print("输入 'quit' 或 'exit' 退出程序")
    print("输入 'debug' 切换到调试模式")
    print("输入 'test' 测试网络连接\n")

    while True:
        try:
            user_input = input("请输入姓名和身份证号 (用空格分隔): ").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                logger.info("用户退出程序")
                print("程序退出")
                break

            if user_input.lower() == 'debug':
                current_level = logger.level
                if current_level == logging.INFO:
                    logger.setLevel(logging.DEBUG)
                    print("已切换到调试模式 (DEBUG)")
                    logger.debug("调试模式已启用")
                else:
                    logger.setLevel(logging.INFO)
                    print("已切换到普通模式 (INFO)")
                    logger.info("普通模式已启用")
                continue

            if user_input.lower() == 'test':
                print("正在测试网络连接...")
                if await test_connection():
                    print("网络连接正常")
                else:
                    print("网络连接失败")
                continue

            if not user_input:
                continue

            try:
                parts = user_input.split()
                if len(parts) != 2:
                    print("输入格式错误，请输入: 姓名 身份证号")
                    logger.warning(f"用户输入格式错误: {user_input}")
                    continue

                name, id_card = parts
                logger.info(f"用户输入: 姓名={name}, 身份证号={id_card}")

                result = await query_single_person(name, id_card)
                print_result(result)

            except Exception as e:
                error_msg = f"查询过程中出错: {str(e)}"
                print(error_msg)
                logger.error(error_msg, exc_info=True)

        except KeyboardInterrupt:
            logger.info("程序被用户中断")
            print("\n程序被中断")
            break
        except Exception as e:
            error_msg = f"输入处理错误: {str(e)}"
            print(error_msg)
            logger.error(error_msg, exc_info=True)

if __name__ == "__main__":
    logger.info("程序启动")
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}", exc_info=True)
    finally:
        logger.info("程序结束")
